import React from 'react';

const SizeSelector = ({ selectedSize, onSizeChange, disabled = false }) => {
  const sizes = [
    { value: 'S', label: 'Small', description: 'เล็ก' },
    { value: 'M', label: 'Medium', description: 'กลาง' },
    { value: 'L', label: 'Large', description: 'ใหญ่' }
  ];

  return (
    <div className="size-selector">
      <h3>เลือกขนาดหมาก:</h3>
      <div className="size-options">
        {sizes.map(size => (
          <button
            key={size.value}
            className={`size-option ${selectedSize === size.value ? 'selected' : ''}`}
            onClick={() => onSizeChange(size.value)}
            disabled={disabled}
          >
            <div className={`piece-preview ${size.value.toLowerCase()}`}>
              {size.value}
            </div>
            <span>{size.description}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default SizeSelector;
