import React from 'react';

const GameCell = ({ 
  cellStack, 
  topPiece, 
  row, 
  col, 
  onCellClick, 
  canPlace, 
  selectedSize,
  disabled = false 
}) => {
  const handleClick = () => {
    if (!disabled && canPlace) {
      onCellClick(row, col);
    }
  };

  const getCellClass = () => {
    let classes = 'game-cell';
    if (canPlace && !disabled) {
      classes += ' can-place';
    }
    if (!canPlace && !disabled) {
      classes += ' cannot-place';
    }
    if (disabled) {
      classes += ' disabled';
    }
    return classes;
  };

  const renderStack = () => {
    if (!cellStack || cellStack.length === 0) {
      return (
        <div className="empty-cell">
          {canPlace && !disabled && (
            <div className={`preview-piece ${selectedSize.toLowerCase()}`}>
              {selectedSize}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="piece-stack">
        {cellStack.map((piece, index) => (
          <div
            key={index}
            className={`piece ${piece.size.toLowerCase()} ${piece.player.toLowerCase()}`}
            style={{
              zIndex: index + 1,
              transform: `translateY(-${index * 2}px)`
            }}
          >
            {piece.size}
          </div>
        ))}
        {canPlace && !disabled && (
          <div 
            className={`preview-piece ${selectedSize.toLowerCase()}`}
            style={{
              zIndex: cellStack.length + 1,
              transform: `translateY(-${cellStack.length * 2}px)`
            }}
          >
            {selectedSize}
          </div>
        )}
      </div>
    );
  };

  return (
    <div 
      className={getCellClass()}
      onClick={handleClick}
      title={
        canPlace 
          ? `วางหมาก ${selectedSize} ที่ตำแหน่ง (${row + 1}, ${col + 1})`
          : topPiece 
            ? `ไม่สามารถวางหมาก ${selectedSize} ทับ ${topPiece.size} ได้`
            : 'ไม่สามารถวางหมากได้'
      }
    >
      {renderStack()}
    </div>
  );
};

export default GameCell;
