import React from 'react';
import GameCell from './GameCell';

const GameBoard = ({ 
  board, 
  onCellClick, 
  canPlacePiece, 
  selectedSize,
  disabled = false 
}) => {
  const getTopPiece = (cellStack) => {
    return cellStack && cellStack.length > 0 ? cellStack[cellStack.length - 1] : null;
  };

  return (
    <div className="game-board">
      {board.map((row, rowIndex) => (
        <div key={rowIndex} className="board-row">
          {row.map((cell, colIndex) => (
            <GameCell
              key={`${rowIndex}-${colIndex}`}
              cellStack={cell}
              topPiece={getTopPiece(cell)}
              row={rowIndex}
              col={colIndex}
              onCellClick={onCellClick}
              canPlace={canPlacePiece(rowIndex, colIndex, selectedSize)}
              selectedSize={selectedSize}
              disabled={disabled}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default GameBoard;
