import React from 'react';
import GameCell from './GameCell';

const GameBoard = ({ 
  board, 
  onCellClick, 
  canPlacePiece, 
  selectedSize,
  disabled = false 
}) => {
  const getTopPiece = (cellStack) => {
    return cellStack && cellStack.length > 0 ? cellStack[cellStack.length - 1] : null;
  };

  return (
    <div className="game-board">
      {board.flat().map((cell, index) => {
        const rowIndex = Math.floor(index / 3);
        const colIndex = index % 3;
        return (
          <GameCell
            key={`${rowIndex}-${colIndex}`}
            cellStack={cell}
            topPiece={getTopPiece(cell)}
            row={rowIndex}
            col={colIndex}
            onCellClick={onCellClick}
            canPlace={canPlacePiece(rowIndex, colIndex, selectedSize)}
            selectedSize={selectedSize}
            disabled={disabled}
          />
        );
      })}
    </div>
  );
};

export default GameBoard;
