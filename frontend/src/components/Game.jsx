import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Game = () => {
  const { user, logout } = useAuth();

  return (
    <div>
      <nav className="navbar">
        <div className="navbar-content">
          <div className="navbar-brand">T<PERSON>-<PERSON><PERSON>-<PERSON><PERSON> Stack</div>
          <div className="navbar-user">
            <span>Welcome, {user?.username}!</span>
            <button className="btn-logout" onClick={logout}>
              Logout
            </button>
          </div>
        </div>
      </nav>
      
      <div className="container">
        <div className="game-container">
          <h1>Game Coming Soon!</h1>
          <p>The game logic will be implemented in the next phase.</p>
          <p>You are successfully logged in as: <strong>{user?.email}</strong></p>
        </div>
      </div>
    </div>
  );
};

export default Game;
