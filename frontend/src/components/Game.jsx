import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useRoom } from '../contexts/RoomContext';
import useGameLogic from '../hooks/useGameLogic';
import GameBoard from './GameBoard';
import SizeSelector from './SizeSelector';

const Game = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { room } = useRoom();
  const {
    game,
    loading,
    error,
    selectedSize,
    startNewGame,
    makeMove,
    resetGame,
    setSelectedSize,
    setError,
    canPlacePiece,
    getCurrentPlayer,
    isGameFinished,
    getWinner,
    getRemainingPieces,
    loadGameFromRoom
  } = useGameLogic();

  useEffect(() => {
    if (user && room && room.currentGame) {
      // โหลดเกมจาก room
      loadGameFromRoom(room.roomCode);
    } else if (user && !room) {
      // ถ้าไม่มี room ให้กลับไป lobby
      navigate('/lobby');
    } else if (user && !game) {
      // สำหรับ single player mode
      startNewGame();
    }
  }, [user, room]);

  const handleCellClick = async (row, col) => {
    if (isGameFinished()) {
      setError('เกมจบแล้ว! กดปุ่ม "เริ่มเกมใหม่" เพื่อเล่นอีกครั้ง');
      return;
    }

    const result = await makeMove(row, col, selectedSize);
    if (!result.success) {
      // Error จะถูกจัดการใน useGameLogic แล้ว
      return;
    }

    if (result.winner) {
      // แสดงข้อความชนะ
      setTimeout(() => {
        alert(`🎉 ผู้เล่น ${result.winner} ชนะ!`);
      }, 100);
    }
  };

  const handleStartNewGame = async () => {
    setError(null);
    await startNewGame();
  };

  const handleResetGame = async () => {
    setError(null);
    await resetGame();
  };

  const handleLogout = () => {
    logout();
  };

  if (loading && !game) {
    return (
      <div className="game-container">
        <div className="loading">กำลังโหลดเกม...</div>
      </div>
    );
  }

  return (
    <div className="game-container">
      {/* Header */}
      <div className="game-header">
        <div className="user-info">
          <h2>สวัสดี, {user?.username}!</h2>
          <button onClick={handleLogout} className="logout-btn">
            ออกจากระบบ
          </button>
        </div>

        <div className="game-controls">
          <button
            onClick={handleStartNewGame}
            disabled={loading}
            className="control-btn"
          >
            เริ่มเกมใหม่
          </button>
          {game && (
            <button
              onClick={handleResetGame}
              disabled={loading}
              className="control-btn"
            >
              รีเซ็ตเกม
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)} className="close-error">×</button>
        </div>
      )}

      {/* Game Info */}
      {game && (
        <div className="game-info">
          <div className="turn-info">
            {isGameFinished() ? (
              <h3>🎉 ผู้เล่น {getWinner()} ชนะ!</h3>
            ) : (
              <h3>เทิร์นของผู้เล่น: <span className={`player ${getCurrentPlayer().toLowerCase()}`}>{getCurrentPlayer()}</span></h3>
            )}
          </div>

          <div className="game-id">
            Game ID: {game._id}
          </div>
        </div>
      )}

      {/* Size Selector */}
      {game && !isGameFinished() && (
        <SizeSelector
          selectedSize={selectedSize}
          onSizeChange={setSelectedSize}
          disabled={loading}
          remainingPieces={getRemainingPieces()}
        />
      )}

      {/* Game Board */}
      {game && game.board && (
        <GameBoard
          board={game.board}
          onCellClick={handleCellClick}
          canPlacePiece={canPlacePiece}
          selectedSize={selectedSize}
          disabled={loading || isGameFinished()}
        />
      )}

      {/* Loading Overlay */}
      {loading && (
        <div className="loading-overlay">
          <div className="loading">กำลังประมวลผล...</div>
        </div>
      )}
    </div>
  );
};

export default Game;
