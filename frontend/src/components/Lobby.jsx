import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRoom } from '../contexts/RoomContext';
import { useAuth } from '../contexts/AuthContext';

const Lobby = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { room, loading, error, createRoom, joinRoom, leaveRoom, startGame, setError } = useRoom();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const [isJoining, setIsJoining] = useState(false);

  const handleCreateRoom = async () => {
    try {
      await createRoom();
    } catch (err) {
      console.error('Failed to create room:', err);
    }
  };

  const handleJoinRoom = async (e) => {
    e.preventDefault();
    if (!roomCodeInput.trim()) return;
    
    try {
      setIsJoining(true);
      await joinRoom(roomCodeInput.trim().toUpperCase());
      setRoomCodeInput('');
    } catch (err) {
      console.error('Failed to join room:', err);
    } finally {
      setIsJoining(false);
    }
  };

  const handleLeaveRoom = async () => {
    try {
      await leaveRoom();
    } catch (err) {
      console.error('Failed to leave room:', err);
    }
  };

  const handleStartGame = async () => {
    try {
      const game = await startGame();
      if (game) {
        navigate('/game');
      }
    } catch (err) {
      console.error('Failed to start game:', err);
    }
  };

  const isHost = room && user && room.host._id === user.id;
  const canStartGame = room && room.players.length === 2 && isHost;

  if (loading) {
    return (
      <div className="lobby-container">
        <div className="loading">กำลังโหลด...</div>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="lobby-container">
        <div className="lobby-header">
          <h2>🎮 Tic-Tac-Toe Multiplayer</h2>
          <p>เล่นแข่งกันคนละเครื่อง!</p>
        </div>

        {error && (
          <div className="error-message">
            {error}
            <button onClick={() => setError(null)} className="close-error">×</button>
          </div>
        )}

        <div className="lobby-actions">
          <div className="create-room-section">
            <h3>สร้างห้องใหม่</h3>
            <button 
              onClick={handleCreateRoom} 
              className="create-room-btn"
              disabled={loading}
            >
              🏠 สร้างห้อง
            </button>
          </div>

          <div className="join-room-section">
            <h3>เข้าร่วมห้อง</h3>
            <form onSubmit={handleJoinRoom} className="join-room-form">
              <input
                type="text"
                placeholder="ใส่รหัสห้อง (เช่น ABC12)"
                value={roomCodeInput}
                onChange={(e) => setRoomCodeInput(e.target.value.toUpperCase())}
                maxLength={6}
                className="room-code-input"
                disabled={isJoining}
              />
              <button 
                type="submit" 
                className="join-room-btn"
                disabled={!roomCodeInput.trim() || isJoining}
              >
                {isJoining ? '⏳ กำลังเข้าร่วม...' : '🚪 เข้าร่วม'}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="lobby-container">
      <div className="room-info">
        <div className="room-header">
          <h2>🏠 ห้อง: {room.roomCode}</h2>
          <button onClick={handleLeaveRoom} className="leave-room-btn">
            🚪 ออกจากห้อง
          </button>
        </div>

        <div className="room-status">
          <div className="status-badge">
            {room.status === 'waiting' ? '⏳ รอผู้เล่น' : 
             room.status === 'playing' ? '🎮 กำลังเล่น' : '✅ จบแล้ว'}
          </div>
        </div>

        <div className="players-list">
          <h3>ผู้เล่น ({room.players.length}/2)</h3>
          <div className="players">
            {room.players.map((player, index) => (
              <div key={player._id} className="player-card">
                <div className="player-info">
                  <span className="player-symbol">{index === 0 ? '❌' : '⭕'}</span>
                  <span className="player-name">{player.username}</span>
                  {player._id === room.host._id && (
                    <span className="host-badge">👑 Host</span>
                  )}
                  {player._id === user.id && (
                    <span className="you-badge">คุณ</span>
                  )}
                </div>
              </div>
            ))}
            
            {room.players.length < 2 && (
              <div className="player-card empty">
                <div className="player-info">
                  <span className="player-symbol">⭕</span>
                  <span className="player-name waiting">รอผู้เล่น...</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {room.status === 'waiting' && (
          <div className="room-actions">
            {canStartGame ? (
              <button 
                onClick={handleStartGame} 
                className="start-game-btn"
                disabled={loading}
              >
                🚀 เริ่มเกม
              </button>
            ) : (
              <div className="waiting-message">
                {room.players.length < 2 ? (
                  <p>📢 แชร์รหัสห้อง <strong>{room.roomCode}</strong> ให้เพื่อนเพื่อเข้าร่วม!</p>
                ) : !isHost ? (
                  <p>⏳ รอ Host เริ่มเกม...</p>
                ) : null}
              </div>
            )}
          </div>
        )}

        {error && (
          <div className="error-message">
            {error}
            <button onClick={() => setError(null)} className="close-error">×</button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Lobby;
