* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Auth Forms */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.auth-form {
  background: white;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  font-size: 28px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-link {
  text-align: center;
  margin-top: 20px;
}

.auth-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.auth-link a:hover {
  text-decoration: underline;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
}

.success-message {
  background-color: #efe;
  color: #3c3;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  text-align: center;
}

/* Navigation */
.navbar {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.navbar-brand {
  font-size: 24px;
  font-weight: bold;
  color: #667eea;
}

.navbar-user {
  display: flex;
  align-items: center;
  gap: 15px;
}

.navbar-user span {
  color: #555;
}

.btn-logout {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
}

.btn-logout:hover {
  background: #c82333;
}

/* Game Board Styles */
.game-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Game Header */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.user-info h2 {
  margin: 0;
  font-size: 1.5rem;
}

.logout-btn, .control-btn {
  background: rgba(255,255,255,0.2);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 0.5rem;
}

.logout-btn:hover, .control-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-1px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.close-error {
  background: none;
  border: none;
  color: #c33;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Game Info */
.game-info {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid #e9ecef;
}

.turn-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.player.x {
  color: #e74c3c;
  font-weight: bold;
}

.player.o {
  color: #3498db;
  font-weight: bold;
}

.game-id {
  font-size: 0.9rem;
  color: #666;
  font-family: monospace;
}

/* Size Selector */
.size-selector {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.size-selector h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.size-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.size-option {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 80px;
}

.size-option:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.size-option.selected {
  border-color: #667eea;
  background: #f8f9ff;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.size-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.size-option.unavailable {
  opacity: 0.3;
  background: #f5f5f5;
  border-color: #ddd;
  cursor: not-allowed;
}

.size-option.unavailable:hover {
  transform: none;
  box-shadow: none;
  border-color: #ddd;
}

.remaining-count {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.size-option.unavailable .remaining-count {
  color: #999;
}

.piece-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 1rem;
}

.piece-preview.s {
  width: 30px;
  height: 30px;
  background: #95a5a6;
  font-size: 0.8rem;
}

.piece-preview.m {
  width: 40px;
  height: 40px;
  background: #34495e;
  font-size: 1rem;
}

.piece-preview.l {
  width: 50px;
  height: 50px;
  background: #2c3e50;
  font-size: 1.2rem;
}

/* Game Board */
.game-board {
  display: grid;
  grid-template-columns: repeat(3, 100px);
  grid-template-rows: repeat(3, 100px);
  gap: 10px;
  background: #34495e;
  padding: 15px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
  margin: 0 auto;
  justify-content: center;
}

.board-row {
  display: contents;
}

/* Game Cell */
.game-cell {
  width: 100px;
  height: 100px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  border: 2px solid #e9ecef;
}

.game-cell:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.game-cell.can-place {
  border: 2px solid #27ae60;
  background: #f8fff8;
}

.game-cell.cannot-place {
  border: 2px solid #e74c3c;
  background: #fff8f8;
  cursor: not-allowed;
}

.game-cell.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.game-cell.disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Empty Cell */
.empty-cell {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Piece Stack */
.piece-stack {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

/* Individual Pieces */
.piece {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  border: 2px solid rgba(0,0,0,0.1);
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Piece Sizes */
.piece.s, .preview-piece.s {
  width: 30px;
  height: 30px;
  font-size: 0.8rem;
}

.piece.m, .preview-piece.m {
  width: 45px;
  height: 45px;
  font-size: 1rem;
}

.piece.l, .preview-piece.l {
  width: 60px;
  height: 60px;
  font-size: 1.2rem;
}

/* Player Colors */
.piece.x, .preview-piece.x {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.piece.o, .preview-piece.o {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

/* Preview Piece */
.preview-piece {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  border: 2px dashed rgba(0,0,0,0.3);
  background: rgba(102, 126, 234, 0.7) !important;
  opacity: 0.8;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Loading */
.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: #666;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay .loading {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

/* Duplicate game-board CSS removed */
  gap: 5px;
  margin: 20px 0;
}

.game-cell {
  width: 100px;
  height: 100px;
  border: 2px solid #333;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: white;
  position: relative;
}

.game-cell:hover {
  background: #f0f0f0;
}

.game-info {
  text-align: center;
  margin: 20px 0;
}

.game-info h3 {
  color: #333;
  margin-bottom: 10px;
}

.size-selector {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.size-btn {
  padding: 10px 20px;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
}

.size-btn.active {
  background: #667eea;
  color: white;
}

.size-btn:hover {
  background: #667eea;
  color: white;
}
