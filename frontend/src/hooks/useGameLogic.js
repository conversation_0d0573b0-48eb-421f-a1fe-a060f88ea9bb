import { useState, useEffect } from 'react';
import axios from 'axios';

const useGameLogic = () => {
  const [game, setGame] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedSize, setSelectedSize] = useState('S');

  // สร้างเกมใหม่
  const startNewGame = async (opponentId = null) => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        'http://localhost:5000/api/game/start',
        { opponentId },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      setGame(response.data.game);
      return { success: true, game: response.data.game };
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to start game';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // วางหมาก
  const makeMove = async (row, col, size = selectedSize) => {
    if (!game) {
      setError('No active game');
      return { success: false, error: 'No active game' };
    }

    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `http://localhost:5000/api/game/${game._id}/move`,
        { row, col, size },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      setGame(response.data.game);
      return { 
        success: true, 
        game: response.data.game, 
        winner: response.data.winner 
      };
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to make move';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // รีเซ็ตเกม
  const resetGame = async () => {
    if (!game) {
      setError('No active game');
      return { success: false, error: 'No active game' };
    }

    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `http://localhost:5000/api/game/${game._id}/reset`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      setGame(response.data.game);
      return { success: true, game: response.data.game };
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to reset game';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // ดูสถานะเกม
  const getGame = async (gameId) => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `http://localhost:5000/api/game/${gameId}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
      setGame(response.data.game);
      return { success: true, game: response.data.game };
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to get game';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // ฟังก์ชันช่วยเหลือ
  const getTopPiece = (cellStack) => {
    return cellStack && cellStack.length > 0 ? cellStack[cellStack.length - 1] : null;
  };

  const canPlacePiece = (row, col, size) => {
    if (!game || !game.board) return false;

    const cellStack = game.board[row][col];

    // ตรวจสอบว่าสามารถวางทับได้หรือไม่
    if (cellStack && cellStack.length > 0) {
      const topPiece = getTopPiece(cellStack);
      const sizeOrder = { 'S': 1, 'M': 2, 'L': 3 };
      if (sizeOrder[size] <= sizeOrder[topPiece.size]) {
        return false; // ไม่สามารถวางหมากเล็กทับหมากใหญ่
      }
    }

    // ตรวจสอบจำนวนหมากที่เหลือ
    const currentPlayer = getCurrentPlayer();
    if (game.remainingPieces && game.remainingPieces[currentPlayer]) {
      return game.remainingPieces[currentPlayer][size] > 0;
    }

    return true;
  };

  const getCurrentPlayer = () => {
    return game?.turn || 'X';
  };

  const isGameFinished = () => {
    return game?.winner !== null;
  };

  const getWinner = () => {
    return game?.winner;
  };

  // สร้าง simplified board สำหรับแสดงผล
  const getDisplayBoard = () => {
    if (!game || !game.board) return Array(3).fill(null).map(() => Array(3).fill(null));

    return game.board.map(row =>
      row.map(cell => getTopPiece(cell))
    );
  };

  // ดูจำนวนหมากที่เหลือ
  const getRemainingPieces = (player = null) => {
    if (!game || !game.remainingPieces) return { S: 2, M: 2, L: 2 };

    const targetPlayer = player || getCurrentPlayer();
    return game.remainingPieces[targetPlayer] || { S: 2, M: 2, L: 2 };
  };

  return {
    // State
    game,
    loading,
    error,
    selectedSize,
    
    // Actions
    startNewGame,
    makeMove,
    resetGame,
    getGame,
    setSelectedSize,
    setError,
    
    // Helper functions
    getTopPiece,
    canPlacePiece,
    getCurrentPlayer,
    isGameFinished,
    getWinner,
    getDisplayBoard,
    getRemainingPieces
  };
};

export default useGameLogic;
