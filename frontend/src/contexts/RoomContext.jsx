import { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

const RoomContext = createContext();

export const useRoom = () => {
  const context = useContext(RoomContext);
  if (!context) {
    throw new Error('useRoom must be used within a RoomProvider');
  }
  return context;
};

export const RoomProvider = ({ children }) => {
  const { token } = useAuth();
  const [room, setRoom] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // สร้างห้องใหม่
  const createRoom = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:5000/api/room/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to create room');
      }

      setRoom(data.room);
      return data.room;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // เข้าร่วมห้อง
  const joinRoom = async (roomCode) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:5000/api/room/join', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ roomCode })
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to join room');
      }

      setRoom(data.room);
      return data.room;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ออกจากห้อง
  const leaveRoom = async () => {
    if (!room) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`http://localhost:5000/api/room/${room.roomCode}/leave`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || 'Failed to leave room');
      }

      setRoom(null);
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // เริ่มเกม
  const startGame = async () => {
    if (!room) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`http://localhost:5000/api/room/${room.roomCode}/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to start game');
      }

      // อัปเดตห้องและเกม
      setRoom(data.room);
      return data.game;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // ดูข้อมูลห้องปัจจุบัน
  const getCurrentRoom = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('http://localhost:5000/api/room/my/current', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.status === 404) {
        setRoom(null);
        return null;
      }

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to get room info');
      }

      setRoom(data.room);
      return data.room;
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // รีเฟรชข้อมูลห้อง
  const refreshRoom = async () => {
    if (!room) return;
    
    try {
      const response = await fetch(`http://localhost:5000/api/room/${room.roomCode}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setRoom(data.room);
      }
    } catch (err) {
      console.error('Failed to refresh room:', err);
    }
  };

  // ตรวจสอบห้องปัจจุบันเมื่อ token เปลี่ยน
  useEffect(() => {
    if (token) {
      getCurrentRoom();
    } else {
      setRoom(null);
    }
  }, [token]);

  // Auto refresh ห้องทุก 3 วินาที
  useEffect(() => {
    if (room && room.status === 'waiting') {
      const interval = setInterval(refreshRoom, 3000);
      return () => clearInterval(interval);
    }
  }, [room]);

  const value = {
    room,
    loading,
    error,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    getCurrentRoom,
    refreshRoom,
    setError
  };

  return (
    <RoomContext.Provider value={value}>
      {children}
    </RoomContext.Provider>
  );
};
