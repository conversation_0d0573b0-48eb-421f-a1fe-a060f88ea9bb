✅ 1. Initial Setup
    📦 Project Setup
        [x] สร้างโฟลเดอร์โปรเจกต์ /client (React) และ /server (Node.js)

        [x] npm init -y และติดตั้ง dependencies ในทั้งสองฝั่ง

        [x] สร้าง .env และตั้งค่าเชื่อม MongoDB

        [x] ติดตั้ง Mongoose, Express, CORS, dotenv (server)

        [x] ติดตั้ง Nodemon (dev server)

🔐 2. Auth System (Login/Register)
    Backend (/server)
        [x] สร้าง User model (username, password-hash)

        [x] สร้าง POST /api/auth/register

        [x] สร้าง POST /api/auth/login

        [x] เข้ารหัสรหัสผ่านด้วย bcrypt

        [x] สร้าง JWT Token หลังล็อกอินสำเร็จ

        [x] Middleware: verifyToken

    Frontend (/client)
        [] หน้า Register (ฟอร์ม username / password)

        [] หน้า Login (ฟอร์ม username / password)

        [] บันทึก JWT Token ลง localStorage

        [] redirect ไปหน้าเกมเมื่อ login สำเร็จ

        [] ป้องกันหน้าเกมถ้ายังไม่ได้ล็อกอิน

🎮 3. Game Logic (วางหมากได้ 3 ขนาด ทับกันได้)
Frontend
 สร้างกระดาน 3x3 (Grid)

 คลิกช่องเพื่อเลือกหมากลง

 ก่อนวาง ให้เลือกขนาด (S, M, L)

 เช็กขนาดหมากก่อนวาง (ต้องใหญ่กว่าชิ้นบนสุด)

 แสดงหมากใหญ่สุดของแต่ละช่อง

 สีหมากแยกตามผู้เล่น

 แสดงผู้ชนะ / เทิร์นปัจจุบัน

Game Logic (แนะนำแยกเป็นไฟล์ useGameLogic.js)
 โครงสร้าง board 3x3 ช่อง

 แต่ละช่องเก็บ stack ของหมาก (เรียงจากเล็กไปใหญ่)

 ฟังก์ชันวางหมาก placePiece(row, col, size)

 เช็กเทิร์น currentPlayer

 ตรวจผู้ชนะ จากหมาก “ใหญ่สุด” ในแต่ละช่อง

 รีเซ็ตเกม

🌐 4. API ระบบเกม
MongoDB Models
User
 [x] username (unique)

 [x] passwordHash

 [] games: [ObjectId] (Game references)

Game
 players: [userId1, userId2]

 board: 3x3 Array (stack ของหมากแต่ละช่อง)

 turn: 'X' | 'O'

 winner: 'X' | 'O' | null

 createdAt

Routes
 POST /api/game/start – เริ่มเกมใหม่

 POST /api/game/move – วางหมาก (ส่ง: ตำแหน่ง + ขนาด)

 GET /api/game/:id – ดูสถานะเกม

 GET /api/game/history – ประวัติเกมผู้เล่น

🖼️ 5. Frontend: Game UI & API Integration
 ดึงสถานะเกมจาก API (/api/game/:id)

 แสดงกระดานตามข้อมูลจาก backend

 เมื่อวางหมาก → call API move

 อัปเดต board + ตรวจผู้ชนะ

 แสดงชื่อผู้เล่น / ผู้ชนะ

 ปุ่มเริ่มเกมใหม่ (เรียก /game/start)

🔄 6. State Management (optional ถ้าใช้ Context/Redux)
 สร้าง AuthContext → เช็ก login

 สร้าง GameContext → เก็บ board / turn / winner

 Hook useGame() สำหรับจัดการ logic

📊 7. Game History / สถิติ
 สร้างหน้า "ประวัติเกม"

 เรียก /api/game/history

 แสดงเกมที่ผ่านมา + ชนะ/แพ้

 ปุ่มกดเพื่อดูเกมย้อนหลัง (optional)

🧪 8. ทดสอบ (Testing & Debugging)
 ทดสอบ Register / Login / Auth middleware

 ทดสอบการวางหมาก + เช็กเทิร์น

 ทดสอบผู้ชนะในแนวตั้ง/แนวนอน/ทแยง

 ทดสอบ logic “วางหมากใหญ่กว่าทับหมากเล็ก”

 ทดสอบเกม 2 คน (mock)

 ตรวจสอบ security เบื้องต้น (JWT, input)

🚀 9. Deploy (Optional)
 Deploy Backend → Render / Railway / Vercel Server

 Deploy Frontend → Vercel / Netlify

 ตั้ง .env.production

 เชื่อม API และ URL ให้เรียบร้อย