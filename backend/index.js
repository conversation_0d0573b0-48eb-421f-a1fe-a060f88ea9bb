const mongoose = require('mongoose');
const express  = require('express');
const cors = require('cors');
const app = express();
require("dotenv").config();
const authRoutes = require('./routes/authRoute');
const gameRoutes = require('./routes/gameRoute');

app.use(express.json());
app.use(cors());
app.use("/api/auth", authRoutes);
app.use("/api/game", gameRoutes);

mongoose.connect(process.env.MONGO_URL)
    .then(() => {
        console.log("Connected to MongoDB");
        app.listen(5000, () => {
            console.log("Server is running on port 5000");
        });
})
    .catch((err) => console.log(err));