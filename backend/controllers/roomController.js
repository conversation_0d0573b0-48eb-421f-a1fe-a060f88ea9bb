const Room = require('../models/Room');
const Game = require('../models/Game');

// สร้างห้องใหม่
const createRoom = async (req, res) => {
    try {
        const currentUserId = req.user.userId;
        
        // ตรวจสอบว่าผู้เล่นอยู่ในห้องอื่นหรือไม่
        const existingRoom = await Room.findOne({
            players: currentUserId,
            status: { $in: ['waiting', 'playing'] }
        });
        
        if (existingRoom) {
            return res.status(400).json({ 
                message: "You are already in a room",
                roomCode: existingRoom.roomCode 
            });
        }
        
        // สร้าง room code ใหม่
        const roomCode = await Room.generateRoomCode();
        
        // สร้างห้องใหม่
        const newRoom = new Room({
            roomCode,
            host: currentUserId,
            players: [currentUserId],
            status: 'waiting'
        });
        
        await newRoom.save();
        await newRoom.populate('players', 'username email');
        await newRoom.populate('host', 'username email');
        
        res.status(201).json({
            message: "Room created successfully",
            room: newRoom
        });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// เข้าร่วมห้อง
const joinRoom = async (req, res) => {
    try {
        const { roomCode } = req.body;
        const currentUserId = req.user.userId;
        
        if (!roomCode) {
            return res.status(400).json({ message: "Room code is required" });
        }
        
        // ตรวจสอบว่าผู้เล่นอยู่ในห้องอื่นหรือไม่
        const existingRoom = await Room.findOne({
            players: currentUserId,
            status: { $in: ['waiting', 'playing'] }
        });
        
        if (existingRoom) {
            return res.status(400).json({ 
                message: "You are already in a room",
                roomCode: existingRoom.roomCode 
            });
        }
        
        // หาห้องที่ต้องการเข้าร่วม
        const room = await Room.findOne({ 
            roomCode: roomCode.toUpperCase(),
            status: 'waiting'
        });
        
        if (!room) {
            return res.status(404).json({ message: "Room not found or already started" });
        }
        
        if (room.isFull()) {
            return res.status(400).json({ message: "Room is full" });
        }
        
        // เพิ่มผู้เล่นเข้าห้อง
        room.addPlayer(currentUserId);
        await room.save();
        await room.populate('players', 'username email');
        await room.populate('host', 'username email');
        
        res.json({
            message: "Joined room successfully",
            room: room
        });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// ออกจากห้อง
const leaveRoom = async (req, res) => {
    try {
        const { roomCode } = req.params;
        const currentUserId = req.user.userId;
        
        const room = await Room.findOne({ roomCode: roomCode.toUpperCase() });
        
        if (!room) {
            return res.status(404).json({ message: "Room not found" });
        }
        
        if (!room.hasPlayer(currentUserId)) {
            return res.status(400).json({ message: "You are not in this room" });
        }
        
        // ลบผู้เล่นออกจากห้อง
        const stillExists = room.removePlayer(currentUserId);
        
        if (!stillExists || room.players.length === 0) {
            // ลบห้องถ้าไม่มีผู้เล่น
            await Room.findByIdAndDelete(room._id);
            return res.json({ message: "Left room and room deleted" });
        }
        
        await room.save();
        await room.populate('players', 'username email');
        await room.populate('host', 'username email');
        
        res.json({
            message: "Left room successfully",
            room: room
        });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// ดูข้อมูลห้อง
const getRoomInfo = async (req, res) => {
    try {
        const { roomCode } = req.params;
        
        const room = await Room.findOne({ roomCode: roomCode.toUpperCase() })
            .populate('players', 'username email')
            .populate('host', 'username email')
            .populate('currentGame');
        
        if (!room) {
            return res.status(404).json({ message: "Room not found" });
        }
        
        res.json({ room });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// เริ่มเกม
const startGame = async (req, res) => {
    try {
        const { roomCode } = req.params;
        const currentUserId = req.user.userId;
        
        const room = await Room.findOne({ roomCode: roomCode.toUpperCase() })
            .populate('players', 'username email');
        
        if (!room) {
            return res.status(404).json({ message: "Room not found" });
        }
        
        // ตรวจสอบว่าเป็น host หรือไม่
        if (room.host.toString() !== currentUserId) {
            return res.status(403).json({ message: "Only host can start the game" });
        }
        
        // ตรวจสอบว่ามีผู้เล่นครบหรือไม่
        if (!room.isFull()) {
            return res.status(400).json({ message: "Need 2 players to start the game" });
        }
        
        if (room.status !== 'waiting') {
            return res.status(400).json({ message: "Game already started or finished" });
        }
        
        // สร้างเกมใหม่
        const newGame = new Game({
            players: room.players.map(p => p._id),
            board: Array(3).fill(null).map(() => Array(3).fill(null).map(() => [])),
            remainingPieces: {
                X: { S: room.settings.pieceLimit.S, M: room.settings.pieceLimit.M, L: room.settings.pieceLimit.L },
                O: { S: room.settings.pieceLimit.S, M: room.settings.pieceLimit.M, L: room.settings.pieceLimit.L }
            },
            turn: 'X',
            winner: null,
            roomCode: room.roomCode
        });
        
        await newGame.save();
        
        // อัปเดตห้อง
        room.currentGame = newGame._id;
        room.status = 'playing';
        await room.save();
        
        await newGame.populate('players', 'username email');
        
        res.json({
            message: "Game started successfully",
            game: newGame,
            room: room
        });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// ดูห้องที่ผู้เล่นอยู่
const getMyRoom = async (req, res) => {
    try {
        const currentUserId = req.user.userId;
        
        const room = await Room.findOne({
            players: currentUserId,
            status: { $in: ['waiting', 'playing'] }
        })
        .populate('players', 'username email')
        .populate('host', 'username email')
        .populate('currentGame');
        
        if (!room) {
            return res.status(404).json({ message: "You are not in any room" });
        }
        
        res.json({ room });
        
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
};

module.exports = {
    createRoom,
    joinRoom,
    leaveRoom,
    getRoomInfo,
    startGame,
    getMyRoom
};
