const Game = require('../models/Game');
const User = require('../models/User');

const SIZES = ['S', 'M', 'L'];

// เริ่มเกมใหม่
const startGame = async (req, res) => {
    try {
        const { opponentId } = req.body;
        const currentUserId = req.user.userId;

        // ตรวจสอบว่า opponent มีอยู่จริง
        if (opponentId) {
            const opponent = await User.findById(opponentId);
            if (!opponent) {
                return res.status(404).json({ message: "Opponent not found" });
            }
        }

        // สร้างเกมใหม่
        const newGame = new Game({
            players: opponentId ? [currentUserId, opponentId] : [currentUserId],
            board: Array(3).fill(null).map(() => Array(3).fill(null).map(() => [])),
            remainingPieces: {
                X: { S: 2, M: 2, L: 2 },
                O: { S: 2, M: 2, L: 2 }
            },
            turn: 'X',
            winner: null
        });

        await newGame.save();
        await newGame.populate('players', 'username email');

        res.status(201).json({
            message: "Game created successfully",
            game: newGame
        });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// วางหมาก
const makeMove = async (req, res) => {
    try {
        const { gameId } = req.params;
        const { row, col, size } = req.body;
        const currentUserId = req.user.userId;

        // ตรวจสอบ input
        if (row < 0 || row > 2 || col < 0 || col > 2) {
            return res.status(400).json({ message: "Invalid position" });
        }
        if (!SIZES.includes(size)) {
            return res.status(400).json({ message: "Invalid piece size" });
        }

        const game = await Game.findById(gameId).populate('players', 'username email');
        if (!game) {
            return res.status(404).json({ message: "Game not found" });
        }

        // ตรวจสอบว่าเกมจบแล้วหรือยัง
        if (game.winner) {
            return res.status(400).json({ message: "Game already finished" });
        }

        // ตรวจสอบว่าเป็นผู้เล่นในเกมนี้หรือไม่
        const playerIndex = game.players.findIndex(p => p._id.toString() === currentUserId);
        if (playerIndex === -1) {
            return res.status(403).json({ message: "You are not a player in this game" });
        }

        // ตรวจสอบเทิร์น (สำหรับ single player ให้เล่นได้ทั้ง X และ O)
        let currentPlayer;
        if (game.players.length === 1) {
            // Single player mode - ให้เล่นได้ทั้ง X และ O
            currentPlayer = game.turn;
        } else {
            // Multiplayer mode - เช็คเทิร์นตามผู้เล่น
            currentPlayer = playerIndex === 0 ? 'X' : 'O';
            if (game.turn !== currentPlayer) {
                return res.status(400).json({ message: "Not your turn" });
            }
        }

        // ตรวจสอบว่าสามารถวางหมากได้หรือไม่ (หมากใหญ่ทับหมากเล็กได้)
        const cellStack = game.board[row][col];
        if (cellStack.length > 0) {
            const topPiece = cellStack[cellStack.length - 1];
            const sizeOrder = { 'S': 1, 'M': 2, 'L': 3 };
            if (sizeOrder[size] <= sizeOrder[topPiece.size]) {
                return res.status(400).json({
                    message: "Cannot place piece: must be larger than the top piece"
                });
            }
        }

        // ตรวจสอบจำนวนหมากที่เหลือ
        if (game.remainingPieces[currentPlayer][size] <= 0) {
            return res.status(400).json({
                message: `No more ${size} pieces available for player ${currentPlayer}`
            });
        }

        // วางหมาก
        game.board[row][col].push({
            size: size,
            player: currentPlayer
        });

        // บอก Mongoose ว่า board เปลี่ยนแปลง
        game.markModified('board');

        // ลดจำนวนหมากที่เหลือ
        game.remainingPieces[currentPlayer][size]--;
        game.markModified('remainingPieces');

        // เปลี่ยนเทิร์น
        game.turn = game.turn === 'X' ? 'O' : 'X';

        // ตรวจสอบผู้ชนะ
        const winner = checkWinner(game.board);
        if (winner) {
            game.winner = winner;
        }

        await game.save();

        res.status(200).json({
            message: "Move made successfully",
            game: game,
            winner: winner
        });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// ฟังก์ชันตรวจสอบผู้ชนะ
const checkWinner = (board) => {
    // สร้าง simplified board ที่เก็บแค่ผู้เล่นของหมากบนสุดในแต่ละช่อง
    const topPieces = board.map(row =>
        row.map(cell => cell.length > 0 ? cell[cell.length - 1].player : null)
    );

    // ตรวจแนวนอน
    for (let row = 0; row < 3; row++) {
        if (topPieces[row][0] &&
            topPieces[row][0] === topPieces[row][1] &&
            topPieces[row][1] === topPieces[row][2]) {
            return topPieces[row][0];
        }
    }

    // ตรวจแนวตั้ง
    for (let col = 0; col < 3; col++) {
        if (topPieces[0][col] &&
            topPieces[0][col] === topPieces[1][col] &&
            topPieces[1][col] === topPieces[2][col]) {
            return topPieces[0][col];
        }
    }

    // ตรวจแนวทแยง
    if (topPieces[0][0] &&
        topPieces[0][0] === topPieces[1][1] &&
        topPieces[1][1] === topPieces[2][2]) {
        return topPieces[0][0];
    }

    if (topPieces[0][2] &&
        topPieces[0][2] === topPieces[1][1] &&
        topPieces[1][1] === topPieces[2][0]) {
        return topPieces[0][2];
    }

    return null;
};

// ดูสถานะเกม
const getGame = async (req, res) => {
    try {
        const { gameId } = req.params;
        const game = await Game.findById(gameId).populate('players', 'username email');

        if (!game) {
            return res.status(404).json({ message: "Game not found" });
        }

        res.status(200).json({ game });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// ดูประวัติเกมของผู้เล่น
const getGameHistory = async (req, res) => {
    try {
        const currentUserId = req.user.userId;
        const games = await Game.find({
            players: currentUserId
        })
        .populate('players', 'username email')
        .sort({ createdAt: -1 })
        .limit(20);

        res.status(200).json({ games });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
};

// รีเซ็ตเกม (สำหรับ single player หรือ practice)
const resetGame = async (req, res) => {
    try {
        const { gameId } = req.params;
        const currentUserId = req.user.userId;

        const game = await Game.findById(gameId);
        if (!game) {
            return res.status(404).json({ message: "Game not found" });
        }

        // ตรวจสอบว่าเป็นผู้เล่นในเกมนี้หรือไม่
        const isPlayer = game.players.some(p => p.toString() === currentUserId);
        if (!isPlayer) {
            return res.status(403).json({ message: "You are not a player in this game" });
        }

        // รีเซ็ตเกม
        game.board = Array(3).fill(null).map(() => Array(3).fill(null).map(() => []));
        game.remainingPieces = {
            X: { S: 2, M: 2, L: 2 },
            O: { S: 2, M: 2, L: 2 }
        };
        game.turn = 'X';
        game.winner = null;

        await game.save();
        await game.populate('players', 'username email');

        res.status(200).json({
            message: "Game reset successfully",
            game: game
        });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
};

module.exports = {
    startGame,
    makeMove,
    getGame,
    getGameHistory,
    resetGame,
    checkWinner
};