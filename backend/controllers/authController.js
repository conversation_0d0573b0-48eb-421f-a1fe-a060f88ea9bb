const User = require('../models/User');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        const user = await User.findOne({ email: email });
        if (!user) {
            return res.status(400).json({ message: "User not found" });
        }
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({ message: "Password is incorrect" });
        }
        const payload = {
            userId: user._id,
            username: user.username,
            email: email,
        };
        const token = jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '1h' });

        res.status(200).json({ message: "Login successful", token, user: { id: user._id, username: user.username, email: user.email } });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}

const register = async (req, res) => {
    try {
        const { email, username, password } = req.body;
        const existingUser = await User.findOne({ email: email });
        if (existingUser) {
            return res.status(400).json({ message: "Email already exists" });
        }
        const hash = await bcrypt.hash(password, 10);
        const newUser = await User.create({
            email: email,
            username: username,
            password: hash
        });
        res.status(201).json({ message: "User created successfully", user: { id: newUser._id, username: newUser.username, email: newUser.email } });
    }
    catch (err) {
        res.status(500).json({ message: err.message });
    }
}

module.exports = {
    login,
    register
};