const express = require('express');
const router = express.Router();
const auth = require('../middlewares/authMiddleware');
const {
    createRoom,
    joinRoom,
    leaveRoom,
    getRoomInfo,
    startGame,
    getMyRoom
} = require('../controllers/roomController');

// สร้างห้องใหม่
router.post('/create', auth, createRoom);

// เข้าร่วมห้อง
router.post('/join', auth, joinRoom);

// ออกจากห้อง
router.delete('/:roomCode/leave', auth, leaveRoom);

// ดูข้อมูลห้อง
router.get('/:roomCode', auth, getRoomInfo);

// เริ่มเกม
router.post('/:roomCode/start', auth, startGame);

// ดูห้องที่ผู้เล่นอยู่
router.get('/my/current', auth, getMyRoom);

module.exports = router;
