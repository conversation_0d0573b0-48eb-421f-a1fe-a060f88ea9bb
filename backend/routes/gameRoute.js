const express = require('express');
const router = express.Router();
const gameController = require('../controllers/gameController');
const authMiddleware = require('../middlewares/authMiddleware');

// ใช้ auth middleware สำหรับทุก route
router.use(authMiddleware);

// POST /api/game/start - เริ่มเกมใหม่
router.post('/start', gameController.startGame);

// POST /api/game/:gameId/move - วางหมาก
router.post('/:gameId/move', gameController.makeMove);

// GET /api/game/:gameId - ดูสถานะเกม
router.get('/:gameId', gameController.getGame);

// GET /api/game/history - ดูประวัติเกม
router.get('/history', gameController.getGameHistory);

// POST /api/game/:gameId/reset - รีเซ็ตเกม
router.post('/:gameId/reset', gameController.resetGame);

module.exports = router;