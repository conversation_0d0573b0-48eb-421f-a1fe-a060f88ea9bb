const jwt = require("jsonwebtoken");

const authMiddleware = (req,res,next) => {
    const authHeader = req.header.authorization;
    if(!authHeader){
        return res.status(400).json({message:"no have authHeader"});
    }
    const token = authHeader.spilt(" ")[1]; 
    if(!token) {
        return res.status(400).json({message: "no token provided"})
    }
    try {
        const decode = jwt.decode(token , process.env.JWT_SECERT);
        req.user = decode;
        next();
    }
    catch(err) {
        res.status(500).json({message:"Invaild token"});
    }
}
