const mongoose = require('mongoose');

const pieceSchema = new mongoose.Schema({
  size: { type: String, enum: ['S', 'M', 'L'], required: true },
  player: { type: String, enum: ['X', 'O'], required: true },
}, { _id: false });

const gameSchema = new mongoose.Schema({
  players: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }],

  board: {
    type: [[[pieceSchema]]], // 3x3 Array of stacks
    required: true,
    default: () => Array(3).fill(null).map(() =>
      Array(3).fill(null).map(() => [])
    )
  },

  // จำนวนหมากที่เหลือของแต่ละผู้เล่น
  remainingPieces: {
    type: {
      X: {
        S: { type: Number, default: 2 },
        M: { type: Number, default: 2 },
        L: { type: Number, default: 2 }
      },
      O: {
        S: { type: Number, default: 2 },
        M: { type: Number, default: 2 },
        L: { type: Number, default: 2 }
      }
    },
    default: {
      X: { S: 2, M: 2, L: 2 },
      O: { S: 2, M: 2, L: 2 }
    }
  },

  turn: { type: String, enum: ['X', 'O'], default: 'X' },
  winner: { type: String, enum: ['X', 'O', null], default: null },
  roomCode: { type: String, default: null },
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Game', gameSchema);
