const mongoose = require('mongoose');

const pieceSchema = new mongoose.Schema({
  size: { type: String, enum: ['S', 'M', 'L'], required: true },
  player: { type: String, enum: ['X', 'O'], required: true },
}, { _id: false });

const gameSchema = new mongoose.Schema({
  players: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }],

  board: {
    type: [[[pieceSchema]]], // 3x3 Array of stacks
    required: true,
    default: () => Array(3).fill(null).map(() =>
      Array(3).fill(null).map(() => [])
    )
  },

  turn: { type: String, enum: ['X', 'O'], default: 'X' },
  winner: { type: String, enum: ['X', 'O', null], default: null },
  createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Game', gameSchema);
