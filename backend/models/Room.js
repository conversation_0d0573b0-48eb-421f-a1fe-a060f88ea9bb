const mongoose = require('mongoose');

const roomSchema = new mongoose.Schema({
  roomCode: { 
    type: String, 
    required: true, 
    unique: true,
    uppercase: true,
    minlength: 4,
    maxlength: 6
  },
  
  host: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  
  players: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  }],
  
  maxPlayers: { 
    type: Number, 
    default: 2,
    min: 2,
    max: 2 
  },
  
  status: { 
    type: String, 
    enum: ['waiting', 'playing', 'finished'], 
    default: 'waiting' 
  },
  
  currentGame: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Game',
    default: null 
  },
  
  settings: {
    pieceLimit: {
      S: { type: Number, default: 2 },
      M: { type: Number, default: 2 },
      L: { type: Number, default: 2 }
    },
    allowStacking: { type: Boolean, default: true }
  },
  
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// Generate unique room code
roomSchema.statics.generateRoomCode = async function() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let roomCode;
  let isUnique = false;
  
  while (!isUnique) {
    roomCode = '';
    for (let i = 0; i < 5; i++) {
      roomCode += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    const existingRoom = await this.findOne({ roomCode });
    if (!existingRoom) {
      isUnique = true;
    }
  }
  
  return roomCode;
};

// Update timestamp on save
roomSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Check if room is full
roomSchema.methods.isFull = function() {
  return this.players.length >= this.maxPlayers;
};

// Check if user is in room
roomSchema.methods.hasPlayer = function(userId) {
  return this.players.some(player => player.toString() === userId.toString());
};

// Add player to room
roomSchema.methods.addPlayer = function(userId) {
  if (!this.isFull() && !this.hasPlayer(userId)) {
    this.players.push(userId);
    return true;
  }
  return false;
};

// Remove player from room
roomSchema.methods.removePlayer = function(userId) {
  this.players = this.players.filter(player => player.toString() !== userId.toString());
  
  // If host leaves, assign new host or delete room
  if (this.host.toString() === userId.toString()) {
    if (this.players.length > 0) {
      this.host = this.players[0];
    } else {
      // Room will be deleted by caller
      return false;
    }
  }
  return true;
};

module.exports = mongoose.model('Room', roomSchema);
